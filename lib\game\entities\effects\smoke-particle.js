ig.module('game.entities.effects.smoke-particle')
.requires(
    'impact.entity',
    'impact.entity-pool'
)
.defines(function () {
    "use strict";

    EntitySmokeParticle = ig.global.EntitySmokeParticle = ig.Entity.extend({
        classId: 'EntitySmokeParticle',
        
        // Entity properties
        size: { x: 8, y: 8 },
        type: ig.Entity.TYPE.NONE,
        checkAgainst: ig.Entity.TYPE.NONE,
        collides: ig.Entity.COLLIDES.NEVER,
        gravityFactor: 0,
        zIndex: 5, // Behind trucks but above background
        
        // Particle properties
        maxLifetime: 6.0, // Maximum lifetime in seconds
        lifetime: 0,
        fadeStartTime: 1.0, // When to start fading (in seconds)

        // Visual properties
        baseSize: 6,
        maxSize: 20,
        currentSize: 6,
        alpha: 0.4,
        baseAlpha: 0.4,

        // Movement properties
        driftVel: { x: 0, y: 0 },
        maxDriftSpeed: 25,

        // Color properties
        baseColor: { r: 160, g: 160, b: 160 }, // Medium gray
        currentColor: { r: 160, g: 160, b: 160 },
        
        // State
        isActive: false,
        
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.reset(x, y, settings);
        },
        
        reset: function (x, y, settings) {
            // Reset position
            this.pos.x = x;
            this.pos.y = y;
            this.last.x = x;
            this.last.y = y;
            
            // Reset particle properties
            this.lifetime = 0;
            this.isActive = true;
            
            // Randomize lifetime
            this.maxLifetime = 1.5 + Math.random() * 1.0; // 1.5 to 2.5 seconds
            this.fadeStartTime = this.maxLifetime * 0.3; // Start fading at 30% of lifetime
            
            // Reset size
            this.currentSize = this.baseSize;
            
            // Reset alpha and color
            this.alpha = this.baseAlpha;
            this.currentColor.r = this.baseColor.r;
            this.currentColor.g = this.baseColor.g;
            this.currentColor.b = this.baseColor.b;
            
            // Set random drift velocity
            var angle = Math.random() * Math.PI * 2;
            var speed = Math.random() * this.maxDriftSpeed;
            this.driftVel.x = Math.cos(angle) * speed;
            this.driftVel.y = Math.sin(angle) * speed - 10; // Slight upward bias
            
            // Apply any custom settings
            if (settings) {
                if (settings.driftVel) {
                    this.driftVel.x = settings.driftVel.x || this.driftVel.x;
                    this.driftVel.y = settings.driftVel.y || this.driftVel.y;
                }
                if (settings.baseColor) {
                    this.baseColor.r = settings.baseColor.r || this.baseColor.r;
                    this.baseColor.g = settings.baseColor.g || this.baseColor.g;
                    this.baseColor.b = settings.baseColor.b || this.baseColor.b;
                    this.currentColor.r = this.baseColor.r;
                    this.currentColor.g = this.baseColor.g;
                    this.currentColor.b = this.baseColor.b;
                }
            }
        },
        
        update: function () {
            if (!this.isActive) return;
            
            this.parent();
            
            // Update lifetime
            this.lifetime += ig.system.tick;
            
            // Check if particle should die
            if (this.lifetime >= this.maxLifetime) {
                this.deactivate();
                return;
            }
            
            // Update movement with drift
            this.vel.x = this.driftVel.x;
            this.vel.y = this.driftVel.y;
            
            // Apply some drag to slow down over time
            this.driftVel.x *= 0.98;
            this.driftVel.y *= 0.98;
            
            // Update visual properties based on lifetime
            this.updateVisuals();
        },
        
        updateVisuals: function () {
            var lifeProgress = this.lifetime / this.maxLifetime;
            
            // Grow the particle over time
            var sizeProgress = Math.min(1, lifeProgress * 2); // Grow quickly in first half of life
            this.currentSize = this.baseSize + (this.maxSize - this.baseSize) * sizeProgress;
            
            // Fade out in the later part of lifetime
            if (this.lifetime > this.fadeStartTime) {
                var fadeProgress = (this.lifetime - this.fadeStartTime) / (this.maxLifetime - this.fadeStartTime);
                this.alpha = this.baseAlpha * (1 - fadeProgress);
            }
            
            // Lighten color over time (smoke disperses and becomes lighter)
            var colorProgress = lifeProgress * 0.3; // Subtle color change
            this.currentColor.r = Math.min(255, this.baseColor.r + colorProgress * 75);
            this.currentColor.g = Math.min(255, this.baseColor.g + colorProgress * 75);
            this.currentColor.b = Math.min(255, this.baseColor.b + colorProgress * 75);
        },
        
        draw: function () {
            if (!this.isActive || this.alpha <= 0) return;
            
            var ctx = ig.system.context;
            var x = this.pos.x - ig.game.screen.x;
            var y = this.pos.y - ig.game.screen.y;
            
            // Skip drawing if off-screen
            if (x + this.currentSize < 0 || x - this.currentSize > ig.system.width ||
                y + this.currentSize < 0 || y - this.currentSize > ig.system.height) {
                return;
            }
            
            ctx.save();
            
            // Set alpha
            ctx.globalAlpha = this.alpha;
            
            // Create radial gradient for smoke effect
            var gradient = ctx.createRadialGradient(x, y, 0, x, y, this.currentSize / 2);
            gradient.addColorStop(0, 'rgba(' + Math.floor(this.currentColor.r) + ',' + 
                                     Math.floor(this.currentColor.g) + ',' + 
                                     Math.floor(this.currentColor.b) + ',' + this.alpha + ')');
            gradient.addColorStop(1, 'rgba(' + Math.floor(this.currentColor.r) + ',' + 
                                     Math.floor(this.currentColor.g) + ',' + 
                                     Math.floor(this.currentColor.b) + ',0)');
            
            // Draw the particle as a circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, this.currentSize / 2, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        },
        
        deactivate: function () {
            this.isActive = false;
            // Return to pool if pooling is enabled
            if (this.erase) {
                this.erase();
            } else {
                this.kill();
            }
        },
        
        // Required for entity pooling
        erase: function () {
            this.isActive = false;
            ig.EntityPool.putInPool(this);
        }
    });
    
    // Enable entity pooling for performance
    ig.EntityPool.enableFor(EntitySmokeParticle);
});
