ig.module('game.entities.effects.smoke-particle')
.requires(
    'impact.entity',
    'impact.entity-pool'
)
.defines(function () {
    "use strict";

    EntitySmokeParticle = ig.global.EntitySmokeParticle = ig.Entity.extend({
        classId: 'EntitySmokeParticle',
        
        // Entity properties
        size: { x: 8, y: 8 },
        type: ig.Entity.TYPE.NONE,
        checkAgainst: ig.Entity.TYPE.NONE,
        collides: ig.Entity.COLLIDES.NEVER,
        gravityFactor: 0,
        zIndex: 5, // Behind trucks but above background
        
        // Particle properties
        maxLifetime: 3.0, // Maximum lifetime in seconds
        lifetime: 0,
        fadeStartTime: 1.0, // When to start fading (in seconds)

        // Visual properties
        baseSize: 12,
        maxSize: 40,
        currentSize: 12,
        alpha: 0.7,
        baseAlpha: 0.7,

        // Movement properties
        driftVel: null, // Will be initialized in reset() to avoid object sharing
        maxDriftSpeed: 25,

        // Color properties
        baseColor: { r: 160, g: 160, b: 160 }, // Medium gray
        currentColor: { r: 160, g: 160, b: 160 },
        
        // State
        isActive: false,
        
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            this.reset(x, y, settings);
        },
        
        reset: function (x, y, settings) {
            // Reset position
            this.pos.x = x;
            this.pos.y = y;
            this.last.x = x;
            this.last.y = y;
            
            // Reset particle properties
            this.lifetime = 0;
            this.isActive = true;
            
            // Use the class default lifetime (don't override the 6.0 seconds set in class definition)
            // Only randomize if no specific lifetime was set
            if (!this.maxLifetime || this.maxLifetime === 0) {
                this.maxLifetime = 3.0; // Use class default
            }
            // Add slight randomization to the class default
            // this.maxLifetime = this.maxLifetime; // ±0.5 seconds variation
            this.fadeStartTime = this.maxLifetime * 0.17; // Start fading at 17% of lifetime (1 second for 6-second lifetime)
            
            // Reset size
            this.currentSize = this.baseSize;
            
            // Reset alpha and color
            this.alpha = this.baseAlpha;
            this.currentColor.r = this.baseColor.r;
            this.currentColor.g = this.baseColor.g;
            this.currentColor.b = this.baseColor.b;
            
            // Initialize drift velocity object (avoid sharing between particles)
            this.driftVel = { x: 0, y: 0 };

            // Set default random drift velocity (will be overridden by settings if provided)
            var angle = Math.random() * Math.PI * 2;
            var speed = Math.random() * this.maxDriftSpeed * 0.5; // Reduced default speed
            this.driftVel.x = Math.cos(angle) * speed;
            this.driftVel.y = Math.sin(angle) * speed;
            
            // Apply any custom settings
            if (settings) {
                if (settings.driftVel) {
                    // Properly handle zero values - use !== undefined instead of ||
                    this.driftVel.x = (settings.driftVel.x !== undefined) ? settings.driftVel.x : this.driftVel.x;
                    this.driftVel.y = (settings.driftVel.y !== undefined) ? settings.driftVel.y : this.driftVel.y;
                }
                if (settings.baseColor) {
                    this.baseColor.r = settings.baseColor.r || this.baseColor.r;
                    this.baseColor.g = settings.baseColor.g || this.baseColor.g;
                    this.baseColor.b = settings.baseColor.b || this.baseColor.b;
                    this.currentColor.r = this.baseColor.r;
                    this.currentColor.g = this.baseColor.g;
                    this.currentColor.b = this.baseColor.b;
                }
            }
        },
        
        update: function () {
            if (!this.isActive) return;
            
            this.parent();
            
            // Update lifetime
            this.lifetime += ig.system.tick;
            
            // Check if particle should die
            if (this.lifetime >= this.maxLifetime) {
                this.deactivate();
                return;
            }
            
            // Update movement with drift (ensure driftVel exists)
            if (this.driftVel) {
                this.vel.x = this.driftVel.x;
                this.vel.y = this.driftVel.y;

                // Apply some drag to slow down over time
                this.driftVel.x *= 0.98;
                this.driftVel.y *= 0.98;
            } else {
                // Fallback if driftVel is corrupted
                this.vel.x = 0;
                this.vel.y = 0;
            }
            
            // Update visual properties based on lifetime
            this.updateVisuals();
        },
        
        updateVisuals: function () {
            var lifeProgress = this.lifetime / this.maxLifetime;
            
            // Grow the particle over time
            var sizeProgress = Math.min(1, lifeProgress * 2); // Grow quickly in first half of life
            this.currentSize = this.baseSize + (this.maxSize - this.baseSize) * sizeProgress;
            
            // Fade out in the later part of lifetime
            if (this.lifetime > this.fadeStartTime) {
                var fadeProgress = (this.lifetime - this.fadeStartTime) / (this.maxLifetime - this.fadeStartTime);
                this.alpha = this.baseAlpha * (1 - fadeProgress);
            }
            
            // Lighten color over time (smoke disperses and becomes lighter)
            var colorProgress = lifeProgress * 0.3; // Subtle color change
            this.currentColor.r = Math.min(255, this.baseColor.r + colorProgress * 75);
            this.currentColor.g = Math.min(255, this.baseColor.g + colorProgress * 75);
            this.currentColor.b = Math.min(255, this.baseColor.b + colorProgress * 75);
        },
        
        draw: function () {
            if (!this.isActive || this.alpha <= 0) return;
            
            var ctx = ig.system.context;
            var x = this.pos.x - ig.game.screen.x;
            var y = this.pos.y - ig.game.screen.y;
            
            // Skip drawing if off-screen
            if (x + this.currentSize < 0 || x - this.currentSize > ig.system.width ||
                y + this.currentSize < 0 || y - this.currentSize > ig.system.height) {
                return;
            }
            
            ctx.save();
            
            // Set alpha
            ctx.globalAlpha = this.alpha;
            
            // Create radial gradient for smoke effect
            var gradient = ctx.createRadialGradient(x, y, 0, x, y, this.currentSize / 2);
            gradient.addColorStop(0, 'rgba(' + Math.floor(this.currentColor.r) + ',' + 
                                     Math.floor(this.currentColor.g) + ',' + 
                                     Math.floor(this.currentColor.b) + ',' + this.alpha + ')');
            gradient.addColorStop(1, 'rgba(' + Math.floor(this.currentColor.r) + ',' + 
                                     Math.floor(this.currentColor.g) + ',' + 
                                     Math.floor(this.currentColor.b) + ',0)');
            
            // Draw the particle as a circle
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(x, y, this.currentSize / 2, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.restore();
        },
        
        deactivate: function () {
            this.isActive = false;
            // Return to pool if pooling is enabled
            if (this.erase) {
                this.erase();
            } else {
                this.kill();
            }
        },
        
        // Required for entity pooling
        erase: function () {
            this.isActive = false;
            ig.EntityPool.putInPool(this);
        }
    });
    
    // Enable entity pooling for performance
    ig.EntityPool.enableFor(EntitySmokeParticle);
});
