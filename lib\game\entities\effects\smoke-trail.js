ig.module('game.entities.effects.smoke-trail')
.requires(
    'plugins.utils.entity-extended',
    'game.entities.effects.smoke-particle'
)
.defines(function () {
    "use strict";

    EntitySmokeTrail = ig.global.EntitySmokeTrail = ig.EntityExtended.extend({
        // Entity properties
        size: { x: 1, y: 1 }, // Minimal size since this is just a manager
        type: ig.Entity.TYPE.NONE,
        checkAgainst: ig.Entity.TYPE.NONE,
        collides: ig.Entity.COLLIDES.NEVER,
        gravityFactor: 0,
        zIndex: 4, // Behind particles but above background
        
        // Trail properties
        targetEntity: null, // The entity this trail follows (truck)
        emissionRate: 10, // Particles per second when moving at full speed
        maxParticles: 70, // Maximum number of active particles
        
        // Emission control
        emissionTimer: 0,
        emissionInterval: 0.1, // Time between emissions (1/emissionRate)
        isEmitting: false,
        
        // Position tracking
        lastEmissionPos: { x: 0, y: 0 },
        emissionOffset: { x: 0, y: 0 }, // Offset from target entity center to emission point
        
        // Speed-based emission
        minSpeedForEmission: 15, // Minimum speed to start emitting
        lastTargetPos: { x: 0, y: 0 },
        targetSpeed: 0,
        
        // Particle management
        activeParticles: [],
        
        init: function (x, y, settings) {
            this.parent(x, y, settings);
            
            // Apply settings
            if (settings) {
                this.targetEntity = settings.targetEntity || null;
                this.emissionRate = settings.emissionRate || this.emissionRate;
                this.maxParticles = settings.maxParticles || this.maxParticles;
                this.emissionOffset.x = settings.emissionOffset ? settings.emissionOffset.x || 0 : 0;
                this.emissionOffset.y = settings.emissionOffset ? settings.emissionOffset.y || 0 : 0;
            }
            
            // Calculate emission interval
            this.emissionInterval = 1.0 / this.emissionRate;
            
            // Initialize position tracking
            if (this.targetEntity) {
                this.updateTargetTracking();
            }
            
            this.activeParticles = [];
        },
        
        update: function () {
            if (ig.game.isPaused) return;
            this.parent();
            
            if (!this.targetEntity || this.targetEntity._killed) {
                this.stopEmission();
                return;
            }
            
            // Update target tracking and speed calculation
            this.updateTargetTracking();
            
            // Update emission state based on target speed
            this.updateEmissionState();
            
            // Handle particle emission
            if (this.isEmitting) {
                this.updateEmission();
            }
            
            // Clean up inactive particles
            this.cleanupParticles();
        },
        
        updateTargetTracking: function () {
            if (!this.targetEntity) return;
            
            var targetCenter = {
                x: this.targetEntity.pos.x + this.targetEntity.size.x / 2,
                y: this.targetEntity.pos.y + this.targetEntity.size.y / 2
            };
            
            // Calculate speed based on position change
            var deltaX = targetCenter.x - this.lastTargetPos.x;
            var deltaY = targetCenter.y - this.lastTargetPos.y;
            this.targetSpeed = Math.sqrt(deltaX * deltaX + deltaY * deltaY) / ig.system.tick;
            
            // Update last position
            this.lastTargetPos.x = targetCenter.x;
            this.lastTargetPos.y = targetCenter.y;
            
            // Update our position to follow target
            this.pos.x = targetCenter.x;
            this.pos.y = targetCenter.y;
        },
        
        updateEmissionState: function () {
            // Start emitting if target is moving fast enough
            if (this.targetSpeed >= this.minSpeedForEmission) {
                if (!this.isEmitting) {
                    this.startEmission();
                }
            } else {
                if (this.isEmitting) {
                    this.stopEmission();
                }
            }
        },
        
        updateEmission: function () {
            this.emissionTimer += ig.system.tick;

            // Check if it's time to emit a new particle
            if (this.emissionTimer >= this.emissionInterval && this.activeParticles.length < this.maxParticles) {
                // Additional performance check: don't emit if we're at 90% capacity
                var capacityThreshold = Math.floor(this.maxParticles * 0.9);
                if (this.activeParticles.length < capacityThreshold || this.targetSpeed > this.minSpeedForEmission * 2) {
                    this.emitParticle();
                    this.emissionTimer = 0;
                }
            }
        },
        
        emitParticle: function () {
            if (!this.targetEntity) return;
            
            // Calculate emission position with offset and some randomness
            var emissionPos = this.getEmissionPosition();
            
            var particle = ig.game.spawnEntity(EntitySmokeParticle, emissionPos.x, emissionPos.y, {
                driftVel: this.calculateInitialDrift(),
                baseColor: this.getParticleColor()
            });
            
            if (particle) {
                this.activeParticles.push(particle);
            }
        },
        
        getEmissionPosition: function () {
            var targetCenter = {
                x: this.targetEntity.pos.x + this.targetEntity.size.x / 2,
                y: this.targetEntity.pos.y + this.targetEntity.size.y / 2
            };
            
            // Apply rotation-aware offset for exhaust position
            var angle = this.targetEntity.angle || 0;
            // add 90 degrees to the angle to account for the truck's sprite orientation
            angle += Math.PI / 2;
            var rotatedOffset = {
                x: this.emissionOffset.x * Math.cos(angle) - this.emissionOffset.y * Math.sin(angle),
                y: this.emissionOffset.x * Math.sin(angle) + this.emissionOffset.y * Math.cos(angle)
            };
            
            return {
                x: targetCenter.x + rotatedOffset.x + (Math.random() - 0.5) * 10,
                y: targetCenter.y + rotatedOffset.y + (Math.random() - 0.5) * 10
            };
        },
        
        calculateInitialDrift: function () {
            // Base drift with some randomness
            var baseSpeed = 15;
            var angle = Math.random() * Math.PI * 2;
            var speed = baseSpeed + Math.random() * 10;
            
            // Add some influence from target movement
            var targetInfluence = 0.3;
            var targetVelX = this.targetEntity.vel ? this.targetEntity.vel.x * targetInfluence : 0;
            var targetVelY = this.targetEntity.vel ? this.targetEntity.vel.y * targetInfluence : 0;
            
            return {
                x: Math.cos(angle) * speed + targetVelX,
                y: Math.sin(angle) * speed + targetVelY - 20 // Upward bias
            };
        },
        
        getParticleColor: function () {
            // Vary the color slightly for more realistic effect
            var baseGray = 160 + Math.random() * 40; // 160-200 range
            return {
                r: baseGray,
                g: baseGray,
                b: baseGray
            };
        },
        
        cleanupParticles: function () {
            // Remove inactive particles from our tracking array
            // Only cleanup every few frames for performance
            if (!this.cleanupCounter) this.cleanupCounter = 0;
            this.cleanupCounter++;

            if (this.cleanupCounter % 5 === 0) { // Cleanup every 5 frames
                for (var i = this.activeParticles.length - 1; i >= 0; i--) {
                    var particle = this.activeParticles[i];
                    if (!particle || particle._killed || !particle.isActive) {
                        this.activeParticles.splice(i, 1);
                    }
                }
            }
        },
        
        startEmission: function () {
            this.isEmitting = true;
            this.emissionTimer = 0;
        },
        
        stopEmission: function () {
            this.isEmitting = false;
        },
        
        // Public methods for external control
        setEmissionRate: function (rate) {
            this.emissionRate = rate;
            this.emissionInterval = 1.0 / this.emissionRate;
        },
        
        setEmissionOffset: function (offsetX, offsetY) {
            this.emissionOffset.x = offsetX;
            this.emissionOffset.y = offsetY;
        },
        
        forceStopEmission: function () {
            this.stopEmission();
        },
        
        forceStartEmission: function () {
            this.startEmission();
        },
        
        // Cleanup when trail is destroyed
        kill: function () {
            this.stopEmission();
            // Let particles fade naturally, don't force kill them
            this.activeParticles = [];
            this.parent();
        },
        
        // No drawing needed - particles draw themselves
        draw: function () {
            // Trail entity itself doesn't need to draw anything
        }
    });
});
